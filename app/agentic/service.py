from collections.abc import Async<PERSON>terator
from uuid import UUID

from langchain_core.messages import HumanMessage
from sqlalchemy.orm import Session

from app.agentic.exceptions import ThreadOwnershipError
from app.agentic.graph.graph_instance import GraphInstance
from app.agentic.repository import OrganizationMemberThreadRepository
from app.agentic.schemas import (
    ChatRequest,
    ThreadHistoryResponse,
    ThreadRead,
    ThreadsRead,
)
from app.workspace.schemas import OrgEnvironment


class AgentService:
    def __init__(
        self,
        org_id: UUID,
        user_id: UUID,
        org_member_id: UUID,
        environment: OrgEnvironment,
        db_session: Session,
        graph_instance: GraphInstance,
        organization_member_thread_repository: OrganizationMemberThreadRepository,
    ):
        self.org_id = org_id
        self.user_id = user_id
        self.org_member_id = org_member_id
        self.environment = environment
        self.db_session = db_session
        self.graph_instance = graph_instance
        self.organization_member_thread_repository = (
            organization_member_thread_repository
        )

    async def process_message_stream(
        self,
        request: ChatRequest,
    ) -> AsyncIterator[str]:
        input_messages = [HumanMessage(request.message)]
        crm_account_id = request.crm_account_id

        if crm_account_id is None or crm_account_id == "":
            raise ValueError("A crm_account_id is required")

        resume = request.resume
        thread_id = request.thread_id

        if thread_id is None or thread_id == "":
            raise ValueError("A thread_id is required")

        is_new_thread = (
            self.organization_member_thread_repository.get_by_thread_id(thread_id)
            is None
        )

        if is_new_thread:
            self.organization_member_thread_repository.create(
                thread_id=thread_id,
                organization_member_id=self.org_member_id,
                environment_id=self.environment.id,
                crm_account_id=crm_account_id,
            )
            self.db_session.commit()

        graph_input = {
            "messages": input_messages,
            "crm_account_id": crm_account_id,
            "thread_id": thread_id,
            "resume": resume,
            "org_id": self.org_id,
            "user_id": self.user_id,
        }

        sse_event_iterator = self.graph_instance.stream_graph(graph_input)
        return sse_event_iterator

    def get_threads_by_org_member_id(self) -> ThreadsRead | None:
        threads = self.organization_member_thread_repository.get_by_org_member_id(
            self.org_member_id
        )

        if threads is None:
            return None

        threads = [ThreadRead.model_validate(thread.__dict__) for thread in threads]
        return ThreadsRead(threads=threads)

    async def get_thread_history(
        self, thread_id: str, page: int, size: int
    ) -> ThreadHistoryResponse | None:
        thread = self.organization_member_thread_repository.get_by_thread_id(thread_id)
        if not thread or thread.organization_member_id != self.org_member_id:
            raise ThreadOwnershipError(
                "Thread not found or does not belong to the current user"
            )

        return await self.graph_instance.get_historical_messages(thread_id, page, size)
